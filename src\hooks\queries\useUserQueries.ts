import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService, type DashboardStats, type SearchResponse, type UserProfile, type UserTree } from '@/services/user.service';
import { toast } from 'sonner';

// Query Keys
export const userKeys = {
  all: ['users'] as const,
  dashboardStats: () => [...userKeys.all, 'dashboard-stats'] as const,
  search: (params: any) => [...userKeys.all, 'search', params] as const,
  profile: (userId: number) => [...userKeys.all, 'profile', userId] as const,
  tree: (userId: number) => [...userKeys.all, 'tree', userId] as const,
};

// Dashboard Stats Query
export function useDashboardStats() {
  return useQuery({
    queryKey: userKeys.dashboardStats(),
    queryFn: async () => {
      const response = await userService.getDashboardStats();
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// User Search Query
interface UseUserSearchParams {
  username?: string;
  page?: number;
  limit?: number;
  roles?: string;
  includeDescendants?: boolean;
  isBanned?: boolean;
  startDate?: string;
  endDate?: string;
  level?: number;
}

export function useUserSearch(params: UseUserSearchParams) {
  return useQuery({
    queryKey: userKeys.search(params),
    queryFn: async () => {
      const response = await userService.searchUsers(params);
      return response.data;
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    enabled: true, // Always enabled, but can be controlled by the component
  });
}

// User Profile Query
export function useUserProfile(userId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: userKeys.profile(userId),
    queryFn: async () => {
      return await userService.getUserProfile(userId);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: enabled && !!userId,
  });
}

// User Tree Query
export function useUserTree(userId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: userKeys.tree(userId),
    queryFn: async () => {
      const response = await userService.getUserTree(userId);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: enabled && !!userId,
  });
}

// User Ban Mutation
export function useUserBanMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, isBanned }: { userId: number; isBanned: boolean }) => {
      return await userService.banUser(userId, isBanned);
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: userKeys.all });
      queryClient.invalidateQueries({ queryKey: userKeys.dashboardStats() });
      
      toast.success(
        variables.isBanned ? 'User banned successfully' : 'User unbanned successfully'
      );
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to update user ban status');
    },
  });
}

// Change Password Mutation
export function useChangePasswordMutation() {
  return useMutation({
    mutationFn: async (params: {
      user_id: number;
      old_password?: string;
      new_password: string;
      retype_password: string;
    }) => {
      return await userService.changePassword(params);
    },
    onSuccess: () => {
      toast.success('Password changed successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to change password');
    },
  });
}
