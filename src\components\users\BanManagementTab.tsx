'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FaBan, FaUnlock, FaExclamationTriangle } from 'react-icons/fa';
import { useUserBanMutation } from '@/hooks/queries';
import { type UserProfile } from '@/services/user.service';
import { useTranslations } from 'next-intl';

interface BanManagementTabProps {
  userProfile: UserProfile;
  onStatusUpdate: () => Promise<void>;
}

export default function BanManagementTab({ userProfile, onStatusUpdate }: BanManagementTabProps) {
  const t = useTranslations('BanManagementTab');

  // Use React Query mutation
  const banMutation = useUserBanMutation();

  const getActionMessage = (isBanned: boolean, role: string) => {
    const isAdmin = ['superowner','owner','superadmin','admin', 'cashier'].includes(role.toLowerCase());
    if (isBanned) {
      return isAdmin
        ? t('thisActionWillUnbanDescendants')
        : t('thisActionWillUnban');
    }
    return isAdmin
      ? t('thisActionWillBanDescendants')
      : t('thisActionWillBan');
  };

  const getButtonText = (isBanned: boolean, role: string) => {
    const isAdmin = ['superowner','owner','superadmin','admin', 'cashier'].includes(role.toLowerCase());
    if (isBanned) {
      return isAdmin ? t('unbanUserDescendants') : t('unbanUser');
    }
    return isAdmin ? t('banUserDescendants') : t('banUser');
  };

  const handleAction = async () => {
    try {
      await banMutation.mutateAsync({
        userId: userProfile.id,
        isBanned: !userProfile.is_banned
      });

      // Call the callback to notify parent component
      await onStatusUpdate();
    } catch (error: unknown) {
      // Error handling is done in the mutation
      console.error('Ban management error:', error);
    }
  };

  return (
    <div className="p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`rounded-lg p-6 shadow-sm max-w-md ${
          userProfile.is_banned
            ? 'bg-gradient-to-br from-red-50 to-red-100'
            : 'bg-gradient-to-br from-green-50 to-green-100'
        }`}
      >
        <div className="space-y-4">
          {/* Current Status */}
          <div className="flex items-center space-x-3">
            {userProfile.is_banned ? (
              <FaBan className="w-5 h-5 text-red-500 shrink-0" />
            ) : (
              <FaUnlock className="w-5 h-5 text-green-500 shrink-0" />
            )}
            <div>
              <h3 className="font-medium">
                {userProfile.is_banned ? t('currentlyBanned') : t('currentlyActive')}
              </h3>
              <p className="text-sm text-gray-600">
                {userProfile.username}
              </p>
              <span
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max
                ${userProfile.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                  userProfile.role === 'superadmin' ? 'bg-blue-500 text-white' :
                  userProfile.role === 'admin' ? 'bg-yellow-500 text-black' :
                  userProfile.role === 'cashier' ? 'bg-green-500 text-white' :
                  userProfile.role === 'player' ? 'bg-gray-500 text-white' :
                'bg-gray-500 text-white max-w-max'}`}
            >
              {userProfile.role.charAt(0).toUpperCase() + userProfile.role.slice(1)}
            </span>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-yellow-50 border border-yellow-100 rounded-md p-3">
            <div className="flex items-start space-x-2">
              <FaExclamationTriangle className="w-4 h-4 text-yellow-500 mt-0.5 shrink-0" />
              <p className="text-sm text-yellow-800">
                {getActionMessage(userProfile.is_banned, userProfile.role)}
              </p>
            </div>
          </div>

          {/* Action Button */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={handleAction}
            disabled={banMutation.isPending}
            className={`w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition-colors ${
              userProfile.is_banned
                ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                : 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
              banMutation.isPending ? 'opacity-75 cursor-not-allowed' : ''
            }`}
          >
            {banMutation.isPending ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>{t('processing')}</span>
              </div>
            ) : (
              <>
                {userProfile.is_banned ? (
                  <>
                    <FaUnlock className="w-4 h-4 mr-2" />
                    {getButtonText(true, userProfile.role)}
                  </>
                ) : (
                  <>
                    <FaBan className="w-4 h-4 mr-2" />
                    {getButtonText(false, userProfile.role)}
                  </>
                )}
              </>
            )}
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
}
