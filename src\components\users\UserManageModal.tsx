'use client';

import { Fragment, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaUser, FaMoneyBill, FaBan, FaHistory, FaDice, FaSitemap } from 'react-icons/fa';
import { UserProfile } from '@/services/user.service';
import { format } from 'date-fns';
import { useUserProfile } from '@/hooks/queries';
import ChangePasswordForm from './ChangePasswordForm';
import CashManagementTab from './CashManagementTab';
import BanManagementTab from './BanManagementTab';
import TransactionHistoryTab from './TransactionHistoryTab';
import BettingHistoryTab from './BettingHistoryTab';
import UserTreeTab from './UserTreeTab';
import { useTranslations } from 'next-intl';

interface UserManageModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number;
  onUpdate?: () => Promise<void>;
  onManageUser?: (userId: number) => void;
}

type TabType = 'info' | 'cash' | 'ban' | 'transactions' | 'betting' | 'tree';

const tabs: { id: TabType; label: string; icon: React.ReactNode }[] = [
  { id: 'info', label: 'User Info', icon: <FaUser className="w-5 h-5" /> },
  { id: 'cash', label: 'Manage Cash', icon: <FaMoneyBill className="w-5 h-5" /> },
  { id: 'ban', label: 'Ban Management', icon: <FaBan className="w-5 h-5" /> },
  { id: 'transactions', label: 'Transactions', icon: <FaHistory className="w-5 h-5" /> },
  { id: 'betting', label: 'Betting History', icon: <FaDice className="w-5 h-5" /> },
  { id: 'tree', label: 'Tree', icon: <FaSitemap className="w-5 h-5" /> },
];

export default function UserManageModal({
  isOpen,
  onClose,
  userId,
  onUpdate,
  onManageUser
}: UserManageModalProps) {
  const [activeTab, setActiveTab] = useState<TabType>('info');
  const t = useTranslations('UserManageModal');

  // Use React Query for user profile
  const { data: userProfile, isLoading: loading, error } = useUserProfile(userId, isOpen && !!userId);

  const handleClose = async () => {
    if (onUpdate) {
      await onUpdate();
    }
    onClose();
  };

  const handleBalanceUpdate = () => {
    // React Query will automatically refetch when the cache is invalidated
  };

  const handleStatusUpdate = () => {
    // React Query will automatically refetch when the cache is invalidated
  };

  if (loading || !userProfile) {
    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={handleClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/30" />
          </Transition.Child>

          <div className="fixed inset-0 flex items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="bg-white rounded-lg shadow-xl max-w-3xl w-full">
                <div className="p-8 flex justify-center items-center">
                  <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'info':
        return (
          <div className="space-y-6 p-4">
            {userProfile && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('basicInformation')}</h3>
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm text-gray-500">{t('username')}</span>
                          <p className="text-gray-900 font-medium">{userProfile.username}</p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">{t('role')}</span>
                          <p
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max
                ${userProfile.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                  userProfile.role === 'superadmin' ? 'bg-blue-500 text-white' :
                  userProfile.role === 'admin' ? 'bg-yellow-500 text-black' :
                  userProfile.role === 'cashier' ? 'bg-green-500 text-white' :
                  userProfile.role === 'player' ? 'bg-gray-500 text-white' :
                'bg-gray-500 text-white max-w-max'}`}
            >
              {userProfile.role.charAt(0).toUpperCase() + userProfile.role.slice(1)}
            </p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">{t('status')}</span>
                          <p className="text-gray-900 font-medium">
                            {userProfile.is_banned ? (
                              <span className="text-red-500">{t('banned')}</span>
                            ) : userProfile.is_active ? (
                              <span className="text-green-500">{t('active')}</span>
                            ) : (
                              <span className="text-yellow-500">{t('inactive')}</span>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('accountDetails')}</h3>
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm text-gray-500">{t('balance')}</span>
                          <p className="text-gray-900 font-medium">
                            {userProfile.balance} {userProfile.currency}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">{t('lastLogin')}</span>
                          <p className="text-gray-900 font-medium">
                            {format(new Date(userProfile.last_login || new Date().toISOString()), 'dd/MM/yyyy HH:mm')}
                          </p>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">{t('createdAt')}</span>
                          <p className="text-gray-900 font-medium">
                          {format(new Date(userProfile.createdAt || new Date().toISOString()), 'dd/MM/yyyy HH:mm')}
                          </p>
                        </div>
                        {userProfile.childCount !== undefined && (
                          <div>
                            <span className="text-sm text-gray-500">{t('childrens')}</span>
                            <div className="mt-1">
                              <a
                                onClick={() => setActiveTab('tree')}
                                className="text-blue-600 font-medium hover:underline transition-colors cursor-pointer"
                              >
                                {userProfile.childCount}
                              </a>
                            </div>
                          </div>
                        )}
                        <div>
                            <span className="text-sm text-gray-500">{t('hisResponsible')}</span>
                            <div className="mt-1">
                              <a
                                onClick={() => onManageUser && onManageUser(userProfile.parent_id)}
                                className="text-blue-600 font-medium hover:underline transition-colors cursor-pointer"
                              >
                                {userProfile.parent_username}
                              </a>
                            </div>
                          </div>
                      </div>
                    </div>
                  </div>

                  </div>

                  {/* Password Change Section */}
                  <div className="w-full">
                      <ChangePasswordForm
                        userId={userProfile.id}
                      />
                    </div>
                </div>
              </>
            )}
          </div>
        );
      case 'cash':
        return (
          <div className="relative">
            {userProfile && (
              <CashManagementTab
                userProfile={userProfile}
                onBalanceUpdate={handleBalanceUpdate}
              />
            )}
          </div>
        );
      case 'ban':
        return (
          <div className="relative">
            {userProfile && (
              <BanManagementTab
                userProfile={userProfile}
                onStatusUpdate={handleStatusUpdate}
              />
            )}
          </div>
        );
      case 'transactions':
        return userProfile && (
          <TransactionHistoryTab userProfile={userProfile} />
        );
      case 'betting':
        return userProfile && (
          <BettingHistoryTab userProfile={userProfile} />
        );
      case 'tree':
        return userProfile && (
          <UserTreeTab
            userProfile={userProfile}
            onManageUser={onManageUser}
          />
        );
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="fixed inset-0 bg-black/40 backdrop-blur-modal z-40"
          />

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 pointer-events-none"
          >
            <div className="bg-white w-full max-w-3xl rounded-lg shadow-xl overflow-hidden pointer-events-auto">
              {/* Header */}
              <div className="bg-gray-900 px-4 py-3 flex items-center justify-between">
                <h2 className="text-lg font-semibold text-white">{t('manageUser')}</h2>
                <button
                  onClick={handleClose}
                  className="text-gray-400 hover:text-gray-300 focus:outline-none focus:text-gray-300 transition-colors"
                >
                  <FaTimes className="h-5 w-5" />
                </button>
              </div>

              {/* Tabs */}
              <div className="border-b border-gray-200">
                <div className="flex overflow-x-auto">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2 px-4 py-2 border-b-2 text-sm font-medium whitespace-nowrap ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.icon}
                      <span>{t(tab.id)}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Content */}
              <div className="max-h-[calc(100vh-16rem)] overflow-y-auto">
                {renderTabContent()}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
