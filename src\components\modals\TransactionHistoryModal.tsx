'use client';

import { useState, useRef, useMemo } from 'react';
import { format } from 'date-fns';
import { FaArrowRight, FaArrowLeft, FaSync } from 'react-icons/fa';
import { useUserTransactions } from '@/hooks/queries';
import { type Transaction } from '@/services/transaction.service';
import { type User } from '@/services/user.service';
import { useTranslations } from 'next-intl';

interface TransactionHistoryModalProps {
  user: User | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function TransactionHistoryModal({
  user,
  isOpen,
  onClose,
}: TransactionHistoryModalProps) {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const t = useTranslations('transactionHistoryModal');

  // Memoize transaction parameters
  const transactionParams = useMemo(() => ({
    page: currentPage,
    limit: 10,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
  }), [currentPage, startDate, endDate]);

  // Use React Query for data fetching
  const { data, isLoading } = useUserTransactions(
    user?.id || 0,
    transactionParams,
    isOpen && !!user
  );

  const transactions = data?.data || [];
  const pagination = data?.pagination || {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 1,
  };

  const modalRef = useRef<HTMLDivElement>(null);

  const handleOutsideClick = useCallback((e: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  }, [onClose]);

  useEffect(() => {
    document.addEventListener('mousedown', handleOutsideClick);
    return () => document.removeEventListener('mousedown', handleOutsideClick);
  }, [handleOutsideClick]);

  if (!isOpen) return null;
  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto ${isOpen ? 'backdrop-blur-sm' : ''}`}>
      <div className="bg-white rounded-lg w-full max-w-4xl" ref={modalRef}>
        <div className="bg-gray-900 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              {t('transactionHistory', { username: user?.username })}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-300 hover:text-white transition-colors"
            >
              <span className="text-2xl">×</span>
            </button>
          </div>
        </div>

        <div className="p-6 max-h-[calc(100vh-16rem)] overflow-y-auto">
          {/* Filters */}
          <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-end gap-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
              <label className="text-sm font-medium text-gray-700">From:</label>
              <input
                type="date"
                value={startDate ? format(new Date(startDate), 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const selectedDate = e.target.value;
                  const startDate = selectedDate ? `${selectedDate}T00:01` : '';
                  setStartDate(startDate);
                }}
                className="input-field w-full sm:w-40"
              />
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
              <label className="text-sm font-medium text-gray-700">To:</label>
              <input
                type="date"
                value={endDate ? format(new Date(endDate), 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const selectedDate = e.target.value;
                  const endDate = selectedDate ? `${selectedDate}T23:59` : '';
                  setEndDate(endDate);
                }}
                className="input-field w-full sm:w-40"
              />
            </div>
            <button
              onClick={() => {
                setStartDate('');
                setEndDate('');
              }}
              className="btn-primary px-3 py-1.5 text-sm w-full sm:w-auto"
              disabled={!startDate && !endDate}
            >
              {t('clear')}
            </button>
          </div>

          {/* Transactions Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('amount')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('from')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('type')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('to')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('date')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-center">
                      <FaSync className="animate-spin h-5 w-5 mx-auto text-gray-500" />
                    </td>
                  </tr>
                ) : transactions.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                      {t('noTransactionsFound')}
                    </td>
                  </tr>
                ) : (
                  transactions.map((transaction) => (
                    <tr key={transaction.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`flex items-center  ${transaction.type === 'add' ? 'text-green-600' : 'text-red-600'}`}>
                          <span className='mr-1'>{new Intl.NumberFormat('en-US').format(parseFloat(transaction.amount))}</span>
                          {new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'TND',}).format(0).replace(/[\d.,]+/g, '')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <span className="font-medium">{transaction.fromUser.username}</span>
                          <span
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max
                ${transaction.fromUser.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                  transaction.fromUser.role === 'superadmin' ? 'bg-blue-500 text-white' :
                  transaction.fromUser.role === 'admin' ? 'bg-yellow-500 text-black' :
                transaction.fromUser.role === 'cashier' ? 'bg-green-500 text-white' :
                transaction.fromUser.role === 'player' ? 'bg-gray-500 text-white' :
                'bg-gray-500 text-white max-w-max'}`}
            >
              {transaction.fromUser.role.charAt(0).toUpperCase() + transaction.fromUser.role.slice(1)}
            </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center gap-1 font-medium ${
                          transaction.type === 'add' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.type === 'add' ? <FaArrowRight /> : <FaArrowLeft />}
                          {transaction.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <span className="font-medium">{transaction.toUser.username}</span>
                          <span
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max
                ${transaction.toUser.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                  transaction.toUser.role === 'superadmin' ? 'bg-blue-500 text-white' :
                  transaction.toUser.role === 'admin' ? 'bg-yellow-500 text-black' :
                  transaction.toUser.role === 'cashier' ? 'bg-green-500 text-white' :
                  transaction.toUser.role === 'player' ? 'bg-gray-500 text-white' :
                'bg-gray-500 text-white max-w-max'}`}
            >
              {transaction.toUser.role.charAt(0).toUpperCase() + transaction.toUser.role.slice(1)}
            </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {format(new Date(transaction.createdAt), 'MMM dd, yyyy HH:mm')}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={pagination.page === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                {t('previous')}
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
                disabled={pagination.page === pagination.totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                {t('next')}
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  {t('showing')} <span className="font-medium">{pagination.page}</span> {t('of')}{' '}
                  <span className="font-medium">{pagination.totalPages}</span> {t('results')}
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={pagination.page === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  >
                    {t('previous')}
                  </button>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
                    disabled={pagination.page === pagination.totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  >
                    {t('next')}
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
