import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GameService, type GameSearchFilters, type PaginatedResponse } from '@/services/game.service';
import { toast } from 'sonner';

// Query Keys
export const gameKeys = {
  all: ['games'] as const,
  search: (filters: GameSearchFilters) => [...gameKeys.all, 'search', filters] as const,
  providers: (type: 'categories' | 'titles') => [...gameKeys.all, 'providers', type] as const,
  history: (userId: number, params: any) => [...gameKeys.all, 'history', userId, params] as const,
};

// Game Search Query
export function useGameSearch(filters: GameSearchFilters) {
  return useQuery({
    queryKey: gameKeys.search(filters),
    queryFn: async () => {
      return await GameService.searchGames(filters);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Providers and Categories Query
export function useProvidersAndCategories(type: 'categories' | 'titles') {
  return useQuery({
    queryKey: gameKeys.providers(type),
    queryFn: async () => {
      return await GameService.getProvidersAndCategories(type);
    },
    staleTime: 30 * 60 * 1000, // 30 minutes (this data doesn't change often)
    gcTime: 60 * 60 * 1000, // 1 hour
  });
}

// Game History Query
interface UseGameHistoryParams {
  viewerId: number;
  targetUserId: number;
  params: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
  };
  enabled?: boolean;
}

export function useGameHistory({ viewerId, targetUserId, params, enabled = true }: UseGameHistoryParams) {
  return useQuery({
    queryKey: gameKeys.history(targetUserId, params),
    queryFn: async () => {
      return await GameService.getGameHistoryByUserId(viewerId, targetUserId, params);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: enabled && !!targetUserId,
  });
}

// Initialize Games Mutation
export function useInitGamesMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      return await GameService.initGames();
    },
    onSuccess: () => {
      // Invalidate all game-related queries to refresh the data
      queryClient.invalidateQueries({ queryKey: gameKeys.all });
      toast.success('Database initialized successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to initialize database');
    },
  });
}
