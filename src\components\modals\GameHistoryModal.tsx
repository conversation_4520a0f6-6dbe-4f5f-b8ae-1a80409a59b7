"use client";

import { useState, useMemo } from 'react';
import { format } from 'date-fns';
import { useGameHistory, useCurrentUserProfile } from '@/hooks/queries';
import { useTranslations } from 'next-intl';

interface GameHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number;
}

interface GameHistory {
  id: number;
  gameId: string;
  name: string;
  bet: number;
  win: number;
  action: string;
  date: Date;
  balance_before: number;
  balance_after: number;
  user: {
    id: number;
    username: string;
  };
}

export default function GameHistoryModal({ isOpen, onClose, userId }: GameHistoryModalProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  const t = useTranslations('gameHistoryModal');

  // Get current user data
  const { data: currentUser } = useCurrentUserProfile();

  // Memoize game history parameters
  const historyParams = useMemo(() => ({
    page: currentPage,
    limit: 10,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
  }), [currentPage, startDate, endDate]);

  // Use React Query for game history
  const { data, isLoading } = useGameHistory({
    viewerId: currentUser?.id || 0,
    targetUserId: userId,
    params: historyParams,
    enabled: isOpen && !!currentUser && !!userId,
  });

  const history = data?.history || [];
  const pagination = {
    page: currentPage,
    limit: 10,
    total: data?.total || 0,
    totalPages: Math.ceil((data?.total || 0) / 10),
    startDate,
    endDate,
  };

  const formatDate = (date: Date) => {
    return format(new Date(date), 'dd/MM/yyyy HH:mm');
  };

  const handleOutsideClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto"
      onClick={handleOutsideClick}
    >
      <div className="bg-white rounded-lg w-full max-w-4xl">
        <div className="bg-gray-900 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              {t('title')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-300 hover:text-white transition-colors"
            >
              <span className="text-2xl">×</span>
            </button>
          </div>
        </div>
        <div className="p-6 max-h-[calc(100vh-16rem)] overflow-y-auto">
          {/* Filters */}
          <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-end gap-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
              <label className="text-sm font-medium text-gray-700">{t('from')}:</label>
              <input
                type="date"
                value={pagination.startDate ? format(new Date(pagination.startDate), 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const selectedDate = e.target.value;
                  const startDate = selectedDate ? `${selectedDate}T00:01` : '';
                  setPagination(prev => ({ ...prev, startDate, page: 1 }));
                }}
                className="input-field w-full sm:w-40"
              />
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
              <label className="text-sm font-medium text-gray-700">{t('to')}:</label>
              <input
                type="date"
                value={pagination.endDate ? format(new Date(pagination.endDate), 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const selectedDate = e.target.value;
                  const endDate = selectedDate ? `${selectedDate}T23:59` : '';
                  setPagination(prev => ({ ...prev, endDate, page: 1 }));
                }}
                className="input-field w-full sm:w-40"
              />
            </div>
            <button
              onClick={() => {
                setStartDate('');
                setEndDate('');
                setCurrentPage(1);
              }}
              className="btn-primary px-3 py-1.5 text-sm w-full sm:w-auto"
              disabled={!startDate && !endDate}
            >
              {t('clear')}
            </button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('game')}</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('bet')}</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('win')}</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('action')}</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('date')}</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('balanceBefore')}</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('balanceAfter')}</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {history.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.bet}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.win}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.action}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(item.date)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.balance_before}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.balance_after}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="mt-4 flex justify-center">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={pagination.page <= 1}
                className="px-4 py-2 mr-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md disabled:opacity-50"
              >
                &lt; {t('previous')}
              </button>
              <span className="px-4 py-2 text-sm text-gray-700">
                {t('page')} {pagination.page} {t('of')} {pagination.totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
                disabled={pagination.page >= pagination.totalPages}
                className="px-4 py-2 ml-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md disabled:opacity-50"
              >
                {t('next')} &gt;
              </button>
            </div>
          )}

          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              {t('close')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
