# React Query Integration Guide

This document explains how TanStack React Query has been integrated into the admin frontend application and how to use it effectively.

## Overview

React Query has been integrated to replace manual data fetching with a powerful caching and synchronization solution. This provides:

- **Automatic caching** - Data is cached and reused across components
- **Background refetching** - Data stays fresh automatically
- **Loading states** - Built-in loading and error states
- **Optimistic updates** - UI updates immediately for better UX
- **Automatic retries** - Failed requests are retried automatically

## Setup

### 1. QueryClient Configuration

The QueryClient is configured in `src/components/QueryProvider.tsx` with:

```typescript
new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
})
```

### 2. Provider Setup

The QueryProvider wraps the entire application in `src/app/[locale]/layout.tsx`:

```typescript
<QueryProvider>
  <NextIntlClientProvider messages={messages}>
    <AuthProvider>
      {/* ... rest of app */}
    </AuthProvider>
  </NextIntlClientProvider>
</QueryProvider>
```

## Query Hooks

### User Queries (`src/hooks/queries/useUserQueries.ts`)

#### Dashboard Stats
```typescript
const { data: stats, isLoading, error } = useDashboardStats();
```

#### User Search
```typescript
const searchParams = useMemo(() => ({
  username: searchTerm.length >= 4 ? searchTerm : undefined,
  page: currentPage,
  limit: 10,
  roles: selectedRoles.length > 0 ? selectedRoles.join(',') : undefined,
  isBanned,
  startDate: startDate || undefined,
  endDate: endDate || undefined,
}), [searchTerm, currentPage, selectedRoles, isBanned, startDate, endDate]);

const { data, isLoading } = useUserSearch(searchParams);
```

#### User Profile
```typescript
const { data: userProfile, isLoading } = useUserProfile(userId, enabled);
```

### Game Queries (`src/hooks/queries/useGameQueries.ts`)

#### Game Search
```typescript
const searchParams = useMemo(() => ({
  page: currentPage,
  limit: 10,
  name: searchTerm.length >= 3 ? searchTerm : undefined,
  title: selectedProvider || undefined,
  categories: selectedCategory || undefined
}), [currentPage, searchTerm, selectedProvider, selectedCategory]);

const { data: gameData, isLoading } = useGameSearch(searchParams);
```

#### Providers and Categories
```typescript
const { data: providers = [] } = useProvidersAndCategories('titles');
const { data: categories = [] } = useProvidersAndCategories('categories');
```

### Transaction Queries (`src/hooks/queries/useTransactionQueries.ts`)

#### User Transactions
```typescript
const transactionParams = useMemo(() => ({
  page: currentPage,
  limit: 10,
  startDate: startDate || undefined,
  endDate: endDate || undefined,
}), [currentPage, startDate, endDate]);

const { data, isLoading } = useUserTransactions(userId, transactionParams, !!userId);
```

## Mutations

### User Ban Mutation
```typescript
const banMutation = useUserBanMutation();

const handleBanUser = async (userId: number, isBanned: boolean) => {
  try {
    await banMutation.mutateAsync({ userId, isBanned });
  } catch (error) {
    // Error handling is done in the mutation
  }
};
```

### Cash Flow Mutations
```typescript
const addCashMutation = useAddCashMutation();
const deductCashMutation = useDeductCashMutation();

const handleAddCash = async (toUserId: number, amount: number) => {
  try {
    await addCashMutation.mutateAsync({ toUserId, amount });
  } catch (error) {
    // Error handling is done in the mutation
  }
};
```

## Best Practices

### 1. Memoize Query Parameters
Always memoize complex query parameters to prevent unnecessary re-renders:

```typescript
const searchParams = useMemo(() => ({
  page: currentPage,
  limit: 10,
  // ... other params
}), [currentPage, /* other dependencies */]);
```

### 2. Use Enabled Option
Control when queries should run using the `enabled` option:

```typescript
const { data } = useUserProfile(userId, !!userId && isModalOpen);
```

### 3. Handle Loading and Error States
```typescript
const { data, isLoading, error } = useQuery(/* ... */);

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
if (!data) return <NoDataMessage />;
```

### 4. Reset Pagination on Filter Changes
```typescript
useEffect(() => {
  setCurrentPage(1);
}, [searchTerm, selectedFilters]);
```

## Cache Management

### Query Keys
Query keys are organized hierarchically:

```typescript
export const userKeys = {
  all: ['users'] as const,
  dashboardStats: () => [...userKeys.all, 'dashboard-stats'] as const,
  search: (params: any) => [...userKeys.all, 'search', params] as const,
  profile: (userId: number) => [...userKeys.all, 'profile', userId] as const,
};
```

### Invalidation
Mutations automatically invalidate related queries:

```typescript
onSuccess: () => {
  queryClient.invalidateQueries({ queryKey: userKeys.all });
  queryClient.invalidateQueries({ queryKey: ['transactions'] });
}
```

## Migration Examples

### Before (Manual State Management)
```typescript
const [data, setData] = useState([]);
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);

const fetchData = useCallback(async () => {
  setLoading(true);
  try {
    const response = await api.getData();
    setData(response.data);
  } catch (err) {
    setError(err);
  } finally {
    setLoading(false);
  }
}, []);

useEffect(() => {
  fetchData();
}, [fetchData]);
```

### After (React Query)
```typescript
const { data = [], isLoading, error } = useQuery({
  queryKey: ['data'],
  queryFn: () => api.getData(),
});
```

## Development Tools

React Query DevTools are enabled in development mode and can be accessed by opening the browser's developer tools. They provide:

- Query cache inspection
- Query invalidation controls
- Performance monitoring
- Network request tracking

## Cache Configuration

Different cache times are used based on data volatility:

- **Dashboard stats**: 2 minutes stale time
- **User searches**: 1 minute stale time
- **Game lists**: 5 minutes stale time
- **Providers/Categories**: 30 minutes stale time (rarely changes)

## Error Handling

Errors are handled at the mutation level with toast notifications:

```typescript
onError: (error: Error) => {
  toast.error(error.message || 'Operation failed');
}
```

## Components Updated

The following components have been successfully migrated to use React Query:

### 1. Dashboard Page (`src/app/[locale]/dashboard/page.tsx`)
- **Before**: Manual state management with `useState` and `useEffect`
- **After**: Uses `useDashboardStats()` hook
- **Benefits**: Automatic caching, background refetching, built-in loading states

### 2. User Search Page (`src/app/[locale]/users/search/page.tsx`)
- **Before**: Complex manual pagination and filtering logic
- **After**: Uses `useUserSearch()` with memoized parameters
- **Benefits**: Cached search results, automatic pagination, optimized re-renders

### 3. Games List Page (`src/app/[locale]/games/list/page.tsx`)
- **Before**: Multiple API calls for games, providers, and categories
- **After**: Uses `useGameSearch()`, `useProvidersAndCategories()`, and `useInitGamesMutation()`
- **Benefits**: Parallel data fetching, cached filter options, optimistic updates

### 4. Transaction History Tab (`src/components/users/TransactionHistoryTab.tsx`)
- **Before**: Manual transaction fetching with pagination
- **After**: Uses `useUserTransactions()` hook
- **Benefits**: Automatic refetching, cached transaction data

### 5. Cash Management Tab (`src/components/users/CashManagementTab.tsx`)
- **Before**: Manual API calls with loading states
- **After**: Uses `useAddCashMutation()` and `useDeductCashMutation()`
- **Benefits**: Built-in loading states, automatic error handling, cache invalidation

## Performance Improvements

1. **Reduced API Calls**: Data is cached and reused across components
2. **Background Updates**: Data stays fresh without user intervention
3. **Optimistic Updates**: UI responds immediately to user actions
4. **Automatic Retries**: Failed requests are retried automatically
5. **Memory Management**: Unused queries are garbage collected

## Code Reduction

The migration resulted in significant code reduction:
- Removed ~200 lines of manual state management code
- Eliminated repetitive loading state logic
- Simplified error handling across components
- Reduced prop drilling for data updates

## Next Steps

To continue improving the application with React Query:

1. **Migrate remaining components** that still use manual data fetching
2. **Implement optimistic updates** for better user experience
3. **Add infinite queries** for large data sets
4. **Configure query invalidation** strategies for real-time updates
5. **Add offline support** using React Query's offline capabilities

This integration provides a much better user experience with automatic caching, background updates, and optimistic UI updates while reducing the amount of boilerplate code needed for data fetching.
