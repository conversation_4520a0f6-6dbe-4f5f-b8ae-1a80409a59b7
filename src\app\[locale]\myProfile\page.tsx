"use client";

import { motion } from 'framer-motion';
import Layout from "@/components/layout/Layout";
import { FaU<PERSON>, FaWallet, FaShieldAlt } from 'react-icons/fa';
import ChangePasswordForm from '@/components/users/ChangePasswordForm';
import { useCurrentUserProfile } from '@/hooks/queries';
import { useTranslations } from 'next-intl';

function MyProfileContent() {
  const t = useTranslations('myProfile');
  const { data: userData, isLoading, error } = useCurrentUserProfile();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !userData) {
    return (
      <div className="text-center py-8 text-gray-600">
        No user data available. Please login again.
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('myProfile')}</h1>
        <p className="text-gray-600">{t('profileInformation')}</p>
      </div>

      <div className="w-full max-w-[95%] bg-white rounded-lg shadow-lg hover:shadow-2xl transition-shadow duration-300 p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* User Information */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-50 rounded-full">
                    <FaUser className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-800">{t('username')}</h3>
                    <p className="text-sm text-gray-600">{userData.username}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-50 rounded-full">
                    <FaShieldAlt className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-800">{t('role')}</h3>
                    <p className="text-sm text-gray-600 capitalize">{userData.role}</p>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-50 rounded-full">
                    <FaWallet className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-800">{t('balance')}</h3>
                    <p className="text-sm text-gray-600">
                      {parseFloat(userData.balance).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })} {userData.currency || 'TND'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-50 rounded-full">
                    <FaShieldAlt className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-gray-800">{t('accountStatus')}</h3>
                    <p className="text-sm text-gray-600">{t('accountIsActive')}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Change Password Form */}
            <div className="mt-6 border-t pt-4">
              <ChangePasswordForm
                userId={userData.id}
                username={userData.username}
              />
            </div>
        </motion.div>
      </div>
    </div>
  );
}

export default function MyProfilePage() {
  return (
    <Layout>
      <MyProfileContent />
    </Layout>
  );
}
