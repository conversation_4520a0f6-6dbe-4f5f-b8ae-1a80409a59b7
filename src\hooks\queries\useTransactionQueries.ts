import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { transactionService, type TransactionParams } from '@/services/transaction.service';
import { cashflowService } from '@/services/cashflow.service';
import { toast } from 'sonner';

// Query Keys
export const transactionKeys = {
  all: ['transactions'] as const,
  myTransactions: (params: TransactionParams) => [...transactionKeys.all, 'my', params] as const,
  userTransactions: (userId: number, params: TransactionParams) => 
    [...transactionKeys.all, 'user', userId, params] as const,
};

// My Transactions Query
export function useMyTransactions(params: TransactionParams) {
  return useQuery({
    queryKey: transactionKeys.myTransactions(params),
    queryFn: async () => {
      return await transactionService.getMyTransactions(params);
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// User Transactions Query
export function useUserTransactions(userId: number, params: TransactionParams, enabled: boolean = true) {
  return useQuery({
    queryKey: transactionKeys.userTransactions(userId, params),
    queryFn: async () => {
      return await transactionService.getUserTransactions(userId, params);
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    enabled: enabled && !!userId,
  });
}

// Add Cash Mutation
export function useAddCashMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: { toUserId: number; amount: number }) => {
      return await cashflowService.addCash(params);
    },
    onSuccess: (data, variables) => {
      // Invalidate transaction queries to refresh the data
      queryClient.invalidateQueries({ queryKey: transactionKeys.all });
      
      // Also invalidate user queries to update balances
      queryClient.invalidateQueries({ queryKey: ['users'] });
      
      toast.success(`Successfully added ${variables.amount} to user account`);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to add cash');
    },
  });
}

// Deduct Cash Mutation
export function useDeductCashMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: { fromUserId: number; amount: number }) => {
      return await cashflowService.deductCash(params);
    },
    onSuccess: (data, variables) => {
      // Invalidate transaction queries to refresh the data
      queryClient.invalidateQueries({ queryKey: transactionKeys.all });
      
      // Also invalidate user queries to update balances
      queryClient.invalidateQueries({ queryKey: ['users'] });
      
      toast.success(`Successfully deducted ${variables.amount} from user account`);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to deduct cash');
    },
  });
}
