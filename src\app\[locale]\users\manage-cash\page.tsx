"use client";

import { useState, useContext, useMemo } from 'react';
import { motion } from 'framer-motion';
import { FaPlus, FaMinus, FaSearch } from 'react-icons/fa';
import { format } from 'date-fns';
import CashManagementModal from '@/components/modals/CashManagementModal';
import { LayoutContext } from '@/components/layout/Layout';
import { useUserSearch } from '@/hooks/queries';
import { type User } from '@/services/user.service';
import { useTranslations } from 'next-intl';


export default function ManageUserCashPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [modalMode, setModalMode] = useState<'add' | 'deduct'>('add');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const { handleBalanceRefresh } = useContext(LayoutContext);
  const t = useTranslations('manageCashPage');

  // Memoize search parameters
  const searchParams = useMemo(() => ({
    username: searchTerm.length >= 4 ? searchTerm : undefined,
    page: currentPage,
    limit: 10,
    level: 1, // Only level 1 users for cash management
  }), [searchTerm, currentPage]);

  // Use React Query for data fetching
  const { data, isLoading } = useUserSearch(searchParams);

  const users = data?.users || [];
  const pagination = data?.pagination || {
    total: 0,
    page: 1,
    totalPages: 1,
    limit: 10
  };



  const formatDate = (dateString: string | undefined) => {
    return format(new Date(dateString || new Date().toISOString()), 'dd/MM/yyyy HH:mm');
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
    // React Query will automatically refetch when needed
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('cashManagement')}</h1>
        <p className="text-gray-600">{t('efficientlyManage')}</p>
      </div>

      <div className="w-full max-w-[95%] bg-white rounded-lg shadow-lg hover:shadow-2xl transition-shadow duration-300 p-6">
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder={t('searchByUsername')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('username')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('role')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('balance')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('status')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('createdAt')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('actions')}</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user, index) => (
                <motion.tr
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={user.is_banned ? 'bg-red-50' : ''}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.username}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${user.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                        user.role === 'superadmin' ? 'bg-blue-500 text-white' :
                        user.role === 'admin' ? 'bg-yellow-500 text-black' :
                        user.role === 'cashier' ? 'bg-green-500 text-white' :
                        user.role === 'player' ? 'bg-gray-500 text-white' :
                        'bg-gray-500 text-white'}`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.balance} {user.currency}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.is_banned
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {user.is_banned ? t('banned') : t('active')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatDate(user.created_at)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => {
                        setSelectedUser(user);
                        setModalMode('add');
                        setIsModalOpen(true);
                      }}
                      disabled={user.is_banned}
                      className={`inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white ${
                        user.is_banned
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                      } w-24`}
                    >
                      <FaPlus className="mr-1" /> {t('add')}
                    </button>
                    <button
                      onClick={() => {
                        setSelectedUser(user);
                        setModalMode('deduct');
                        setIsModalOpen(true);
                      }}
                      disabled={user.is_banned}
                      className={`inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white ${
                        user.is_banned
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'
                      } w-24`}
                    >
                      <FaMinus className="mr-1" /> {t('deduct')}
                    </button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-4 flex items-center justify-center">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => {
              const newPage = pagination.page - 1;
              if (newPage >= 1) {
                setCurrentPage(newPage);
              }
            }}
            disabled={pagination.page <= 1}
            className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
              pagination.page <= 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            {t('previous')}
          </button>
          <span className="text-sm text-gray-700">
            {t('showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('to')}{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} {t('of')} {pagination.total} {t('results')}
          </span>
          <button
            onClick={() => {
              const newPage = pagination.page + 1;
              if (newPage <= pagination.totalPages) {
                setCurrentPage(newPage);
              }
            }}
            disabled={pagination.page >= pagination.totalPages}
            className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
              pagination.page >= pagination.totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            {t('next')}
          </button>
        </div>
      </div>

      {/* Cash Management Modal */}
      {selectedUser && (
        <CashManagementModal
          isOpen={isModalOpen}
          onClose={closeModal}
          user={selectedUser!}
          mode={modalMode}
          onBalanceUpdate={handleBalanceRefresh}
        />
      )}
    </div>
  );
}
