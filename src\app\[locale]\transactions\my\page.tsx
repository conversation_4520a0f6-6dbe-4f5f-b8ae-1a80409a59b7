'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  SortingState,
  Row,
} from '@tanstack/react-table';
import { format } from 'date-fns';
import { FaArrowRight, FaArrowLeft, FaSync, FaFilter } from 'react-icons/fa';
import { useMyTransactions } from '@/hooks/queries';
import { type Transaction } from '@/services/transaction.service';
import { useTranslations } from 'next-intl';

export default function MyTransactionsPage() {
  const [showFilters, setShowFilters] = useState(false);
  const [selectedType, setSelectedType] = useState<'add' | 'deduct' | undefined>(undefined);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [sorting, setSorting] = useState<SortingState>([]);
  const [currentPage, setCurrentPage] = useState(1);

  const t = useTranslations('myTransactions');

  // Memoize transaction parameters
  const transactionParams = useMemo(() => ({
    page: currentPage,
    limit: 10,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
  }), [currentPage, startDate, endDate]);

  // Use React Query for data fetching
  const { data, isLoading, refetch } = useMyTransactions(transactionParams);

  // Filter transactions by type on the client side
  const transactions = useMemo(() => {
    if (!data?.data) return [];
    return data.data.filter(t => !selectedType || t.type === selectedType);
  }, [data?.data, selectedType]);

  const pagination = data?.pagination || {
    total: 0,
    page: 1,
    totalPages: 1,
    limit: 10
  };

  const columns = useMemo(
    () => [
      {
        accessorKey: 'id',
        header: t('id'),
        enableHiding: true,
      },
      {
        accessorKey: 'amount',
        header: t('amount'),
        cell: ({ row }: { row: Row<Transaction> }) => (
          <span className={`${row.original.type === 'add' ? 'text-green-600' : 'text-red-600'}`}>
            {row.original.amount} TND
          </span>
        ),
      },
      {
        accessorKey: 'fromUser',
        header: t('from'),
        cell: ({ row }: { row: Row<Transaction> }) => (
          <div className="flex flex-col">
            <span className="font-medium">{row.original.fromUser.username}</span>
            <span
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max
                ${row.original.fromUser.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                row.original.fromUser.role === 'superadmin' ? 'bg-blue-500 text-white' :
                row.original.fromUser.role === 'admin' ? 'bg-yellow-500 text-black' :
                row.original.fromUser.role === 'cashier' ? 'bg-green-500 text-white' :
                row.original.fromUser.role === 'player' ? 'bg-gray-500 text-white' :
                'bg-gray-500 text-white max-w-max'}`}
            >
              {row.original.fromUser.role.charAt(0).toUpperCase() + row.original.fromUser.role.slice(1)}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'type',
        header: t('type'),
        cell: ({ row }: { row: Row<Transaction> }) => (
          <span className={`inline-flex items-center gap-1 font-medium ${
            row.original.type === 'add' ? 'text-green-600' : 'text-red-600'
          }`}>
            {row.original.type === 'add' ? (
              <FaArrowRight className="text-green-600" />
            ) : (
              <FaArrowLeft className="text-red-600" />
            )}
            {row.original.type.charAt(0).toUpperCase() + row.original.type.slice(1)}
          </span>
        ),
      },
      {
        accessorKey: 'toUser',
        header: t('to'),
        cell: ({ row }: { row: Row<Transaction> }) => (
          <div className="flex flex-col">
            <span className="font-medium">{row.original.toUser.username}</span>
            <span
              className={`px-2 text-xs leading-5 font-semibold rounded-full  max-w-max
                ${row.original.toUser.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                row.original.toUser.role === 'superadmin' ? 'bg-blue-500 text-white' :
                row.original.toUser.role === 'admin' ? 'bg-yellow-500 text-black' :
                row.original.toUser.role === 'cashier' ? 'bg-green-500 text-white' :
                row.original.toUser.role === 'player' ? 'bg-gray-500 text-white' :
                'bg-gray-500 text-white max-w-max'}`}
            >
              {row.original.toUser.role.charAt(0).toUpperCase() + row.original.toUser.role.slice(1)}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'createdAt',
        header: t('date'),
        cell: ({ row }: { row: Row<Transaction> }) => format(new Date(row.original.createdAt), 'dd/MM/yyyy HH:mm'),
      },
    ],
    [t]
  );

  const table = useReactTable({
    data: transactions,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      columnVisibility: {
        id: false
      }
    }
  });

  // Reset to page 1 when filters change
  const resetToFirstPage = () => {
    setCurrentPage(1);
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl md:text-3xl font-bold gradient-text">{t('myTransactions')}</h1>
          <button
            onClick={() => refetch()}
            disabled={isLoading}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            title={t('refreshTransactions')}
          >
            <FaSync className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            title={t('toggleFilters')}
          >
            <FaFilter className={`w-5 h-5 ${showFilters ? 'text-blue-500' : ''}`} />
          </button>
        </div>
        <p className="text-gray-600">{t('viewAndManage')}</p>
      </div>

      {/* Filters */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-white p-4 rounded-lg shadow space-y-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">{t('type')}</label>
              <select
                value={selectedType || ''}
                onChange={(e) => setSelectedType(e.target.value as 'add' | 'deduct' | undefined)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                <option value="">{t('all')}</option>
                <option value="add">{t('add')}</option>
                <option value="deduct">{t('deduct')}</option>
              </select>
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">{t('startDate')}</label>
              <input
                type="date"
                value={startDate ? format(new Date(startDate), 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const selectedDate = e.target.value;
                  const startDate = selectedDate ? `${selectedDate}T00:01` : '';
                  setStartDate(startDate);
                }}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">{t('endDate')}</label>
              <input
                type="date"
                value={endDate ? format(new Date(endDate), 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const selectedDate = e.target.value;
                  const endDate = selectedDate ? `${selectedDate}T23:59` : '';
                  setEndDate(endDate);
                }}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
          </div>
          <div className="flex justify-end">
            <button
              onClick={() => {
                setStartDate('');
                setEndDate('');
              }}
              className="btn-primary px-3 py-1.5 text-sm"
              disabled={!startDate && !endDate}
            >
              {t('clear')}
            </button>
          </div>
        </motion.div>
      )}

      {/* Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow overflow-hidden"
      >
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      onClick={header.column.getToggleSortingHandler()}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    >
                      <div className="flex items-center gap-2">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 whitespace-nowrap">
                    <div className="flex justify-center">
                      <FaSync className="animate-spin h-5 w-5 text-gray-500" />
                    </div>
                  </td>
                </tr>
              ) : transactions.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                    {t('noTransactionsFound')}
                  </td>
                </tr>
              ) : (
                table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="px-6 py-4 whitespace-nowrap">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </motion.div>
        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="mt-4 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={pagination.page <= 1}
                className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                  pagination.page <= 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {t('previous')}
              </button>
              <span className="text-sm text-gray-700">
                {t('showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('toText')} {' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} {t('of')} {pagination.total} {t('results')}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(pagination.totalPages, prev + 1))}
                disabled={pagination.page >= pagination.totalPages}
                className={`flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                  pagination.page >= pagination.totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {t('next')}
              </button>
            </div>
          </div>
        )}
    </div>
  );
}
